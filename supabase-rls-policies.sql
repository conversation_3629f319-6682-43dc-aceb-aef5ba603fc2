-- Supabase RLS Policies Fix
-- Run these SQL commands in your Supabase SQL Editor to fix RLS policy violations

-- =============================================
-- 1. STORAGE BUCKET POLICIES (for image uploads)
-- =============================================

-- Create the profileimg bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'profileimg',
  'profileimg',
  true,
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
)
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 5242880,
  allowed_mime_types = ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

-- Allow authenticated users to upload images
CREATE POLICY "Authenticated users can upload images" ON storage.objects
FOR INSERT TO authenticated
WITH CHECK (bucket_id = 'profileimg');

-- Allow authenticated users to update their own images
CREATE POLICY "Users can update own images" ON storage.objects
FOR UPDATE TO authenticated
USING (bucket_id = 'profileimg' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Allow authenticated users to delete their own images
CREATE POLICY "Users can delete own images" ON storage.objects
FOR DELETE TO authenticated
USING (bucket_id = 'profileimg' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Allow public read access to images (for resume viewing)
CREATE POLICY "Public read access for images" ON storage.objects
FOR SELECT TO public
USING (bucket_id = 'profileimg');

-- =============================================
-- 2. RESUMES TABLE POLICIES
-- =============================================

-- Enable RLS on resumes table
ALTER TABLE resumes ENABLE ROW LEVEL SECURITY;

-- Allow authenticated users to insert their own resumes
CREATE POLICY "Users can insert own resumes" ON resumes
FOR INSERT TO authenticated
WITH CHECK (auth.uid() = user_id);

-- Allow authenticated users to view their own resumes
CREATE POLICY "Users can view own resumes" ON resumes
FOR SELECT TO authenticated
USING (auth.uid() = user_id);

-- Allow public to view public resumes
CREATE POLICY "Public can view public resumes" ON resumes
FOR SELECT TO public
USING (is_public = true);

-- Allow authenticated users to update their own resumes
CREATE POLICY "Users can update own resumes" ON resumes
FOR UPDATE TO authenticated
USING (auth.uid() = user_id);

-- Allow authenticated users to delete their own resumes
CREATE POLICY "Users can delete own resumes" ON resumes
FOR DELETE TO authenticated
USING (auth.uid() = user_id);

-- =============================================
-- 3. ANALYTICS TABLE POLICIES
-- =============================================

-- Enable RLS on analytics table (if exists)
ALTER TABLE IF EXISTS analytics ENABLE ROW LEVEL SECURITY;

-- Allow public to insert analytics (for tracking views)
CREATE POLICY "Public can insert analytics" ON analytics
FOR INSERT TO public
WITH CHECK (true);

-- Allow resume owners to view analytics for their resumes
CREATE POLICY "Users can view own resume analytics" ON analytics
FOR SELECT TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM resumes 
    WHERE resumes.id = analytics.resume_id 
    AND resumes.user_id = auth.uid()
  )
);

-- =============================================
-- 4. TEMPLATES AND THEMES POLICIES
-- =============================================

-- Enable RLS on templates table
ALTER TABLE IF EXISTS templates ENABLE ROW LEVEL SECURITY;

-- Allow public read access to templates
CREATE POLICY "Public can view templates" ON templates
FOR SELECT TO public
USING (true);

-- Enable RLS on themes table
ALTER TABLE IF EXISTS themes ENABLE ROW LEVEL SECURITY;

-- Allow public read access to themes
CREATE POLICY "Public can view themes" ON themes
FOR SELECT TO public
USING (true);

-- =============================================
-- 5. USER PROFILES/SUBSCRIPTIONS POLICIES
-- =============================================

-- Enable RLS on user_subscriptions table (if exists)
ALTER TABLE IF EXISTS user_subscriptions ENABLE ROW LEVEL SECURITY;

-- Allow users to view their own subscription
CREATE POLICY "Users can view own subscription" ON user_subscriptions
FOR SELECT TO authenticated
USING (auth.uid() = user_id);

-- Allow users to update their own subscription
CREATE POLICY "Users can update own subscription" ON user_subscriptions
FOR UPDATE TO authenticated
USING (auth.uid() = user_id);

-- =============================================
-- 6. FUNCTIONS FOR SLUG GENERATION
-- =============================================

-- Create function for generating unique slugs (if not exists)
CREATE OR REPLACE FUNCTION generate_unique_slug(base_title TEXT, user_id_param UUID)
RETURNS TEXT AS $$
DECLARE
  base_slug TEXT;
  final_slug TEXT;
  counter INTEGER := 0;
BEGIN
  -- Convert title to slug format
  base_slug := lower(trim(regexp_replace(base_title, '[^a-zA-Z0-9\s]', '', 'g')));
  base_slug := regexp_replace(base_slug, '\s+', '-', 'g');
  base_slug := trim(base_slug, '-');
  
  -- Ensure slug is not empty
  IF base_slug = '' THEN
    base_slug := 'resume';
  END IF;
  
  final_slug := base_slug;
  
  -- Check for uniqueness and append counter if needed
  WHILE EXISTS (SELECT 1 FROM resumes WHERE slug = final_slug AND user_id = user_id_param) LOOP
    counter := counter + 1;
    final_slug := base_slug || '-' || counter;
  END LOOP;
  
  RETURN final_slug;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION generate_unique_slug(TEXT, UUID) TO authenticated;

-- =============================================
-- 7. ADDITIONAL SECURITY SETTINGS
-- =============================================

-- Ensure storage is properly configured
UPDATE storage.buckets 
SET public = true 
WHERE id = 'profileimg';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_resumes_user_id ON resumes(user_id);
CREATE INDEX IF NOT EXISTS idx_resumes_slug ON resumes(slug);
CREATE INDEX IF NOT EXISTS idx_resumes_public ON resumes(is_public) WHERE is_public = true;

-- =============================================
-- 8. TROUBLESHOOTING QUERIES
-- =============================================

-- Check current user authentication status
-- SELECT auth.uid(), auth.role();

-- Check existing policies
-- SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
-- FROM pg_policies 
-- WHERE schemaname = 'public';

-- Check storage policies
-- SELECT * FROM storage.policies;

-- Check bucket configuration
-- SELECT * FROM storage.buckets WHERE id = 'profileimg';
