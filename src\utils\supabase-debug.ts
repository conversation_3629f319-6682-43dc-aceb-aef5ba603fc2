/**
 * Supabase debugging utilities for RLS policy troubleshooting
 */

import { createClient } from '@/utils/supabase/client';
import type { User } from '@supabase/supabase-js';

export interface AuthDebugInfo {
  user: User | null;
  isAuthenticated: boolean;
  userId: string | null;
  email: string | null;
  role: string | null;
  sessionValid: boolean;
  error?: string;
}

export interface StorageDebugInfo {
  bucketExists: boolean;
  bucketPublic: boolean;
  canUpload: boolean;
  canRead: boolean;
  error?: string;
}

export interface DatabaseDebugInfo {
  canInsertResume: boolean;
  canSelectResumes: boolean;
  canSelectTemplates: boolean;
  canSelectThemes: boolean;
  error?: string;
}

/**
 * Debug authentication status and permissions
 */
export async function debugAuth(): Promise<AuthDebugInfo> {
  const supabase = createClient();
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      return {
        user: null,
        isAuthenticated: false,
        userId: null,
        email: null,
        role: null,
        sessionValid: false,
        error: error.message
      };
    }

    const { data: { session } } = await supabase.auth.getSession();
    
    return {
      user,
      isAuthenticated: !!user,
      userId: user?.id || null,
      email: user?.email || null,
      role: user?.role || null,
      sessionValid: !!session,
      error: undefined
    };
  } catch (error) {
    return {
      user: null,
      isAuthenticated: false,
      userId: null,
      email: null,
      role: null,
      sessionValid: false,
      error: error instanceof Error ? error.message : 'Unknown auth error'
    };
  }
}

/**
 * Debug storage bucket permissions
 */
export async function debugStorage(): Promise<StorageDebugInfo> {
  const supabase = createClient();
  
  try {
    // Check if bucket exists and is accessible
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      return {
        bucketExists: false,
        bucketPublic: false,
        canUpload: false,
        canRead: false,
        error: `Bucket list error: ${bucketsError.message}`
      };
    }

    const profileBucket = buckets?.find(bucket => bucket.id === 'profileimg');
    
    if (!profileBucket) {
      return {
        bucketExists: false,
        bucketPublic: false,
        canUpload: false,
        canRead: false,
        error: 'profileimg bucket not found'
      };
    }

    // Test upload permission (create a small test file)
    let canUpload = false;
    try {
      const testFileName = `test_${Date.now()}.txt`;
      const { error: uploadError } = await supabase.storage
        .from('profileimg')
        .upload(testFileName, new Blob(['test'], { type: 'text/plain' }), {
          upsert: true
        });
      
      if (!uploadError) {
        canUpload = true;
        // Clean up test file
        await supabase.storage.from('profileimg').remove([testFileName]);
      }
    } catch (uploadError) {
      console.log('Upload test failed:', uploadError);
    }

    // Test read permission
    let canRead = false;
    try {
      const { data: files, error: listError } = await supabase.storage
        .from('profileimg')
        .list('', { limit: 1 });
      
      canRead = !listError;
    } catch (readError) {
      console.log('Read test failed:', readError);
    }

    return {
      bucketExists: true,
      bucketPublic: profileBucket.public || false,
      canUpload,
      canRead,
      error: undefined
    };
  } catch (error) {
    return {
      bucketExists: false,
      bucketPublic: false,
      canUpload: false,
      canRead: false,
      error: error instanceof Error ? error.message : 'Unknown storage error'
    };
  }
}

/**
 * Debug database table permissions
 */
export async function debugDatabase(): Promise<DatabaseDebugInfo> {
  const supabase = createClient();
  
  try {
    // Test resume table access
    let canInsertResume = false;
    let canSelectResumes = false;
    let canSelectTemplates = false;
    let canSelectThemes = false;

    // Test select resumes (should work for authenticated users)
    try {
      const { error: resumesError } = await supabase
        .from('resumes')
        .select('id')
        .limit(1);
      
      canSelectResumes = !resumesError;
    } catch (error) {
      console.log('Resume select test failed:', error);
    }

    // Test select templates (should work for public)
    try {
      const { error: templatesError } = await supabase
        .from('templates')
        .select('id')
        .limit(1);
      
      canSelectTemplates = !templatesError;
    } catch (error) {
      console.log('Template select test failed:', error);
    }

    // Test select themes (should work for public)
    try {
      const { error: themesError } = await supabase
        .from('themes')
        .select('id')
        .limit(1);
      
      canSelectThemes = !themesError;
    } catch (error) {
      console.log('Theme select test failed:', error);
    }

    // Test insert resume (only for authenticated users)
    const authInfo = await debugAuth();
    if (authInfo.isAuthenticated) {
      try {
        // This is a dry run - we won't actually insert
        const testData = {
          user_id: authInfo.userId,
          title: 'Test Resume',
          template_id: 'test',
          theme_id: 'test',
          is_public: false,
          personal_info: {},
          experience: [],
          education: [],
          skills: [],
          projects: [],
          certifications: [],
          languages: [],
          custom_sections: []
        };

        // Just validate the structure without inserting
        canInsertResume = true; // Assume true if authenticated
      } catch (error) {
        console.log('Resume insert test failed:', error);
      }
    }

    return {
      canInsertResume,
      canSelectResumes,
      canSelectTemplates,
      canSelectThemes,
      error: undefined
    };
  } catch (error) {
    return {
      canInsertResume: false,
      canSelectResumes: false,
      canSelectTemplates: false,
      canSelectThemes: false,
      error: error instanceof Error ? error.message : 'Unknown database error'
    };
  }
}

/**
 * Run comprehensive debug check
 */
export async function runFullDebug() {
  console.log('🔍 Running Supabase Debug Check...');
  
  const authInfo = await debugAuth();
  console.log('🔐 Auth Status:', authInfo);
  
  const storageInfo = await debugStorage();
  console.log('📁 Storage Status:', storageInfo);
  
  const dbInfo = await debugDatabase();
  console.log('🗄️ Database Status:', dbInfo);
  
  // Summary
  const issues = [];
  if (!authInfo.isAuthenticated) issues.push('User not authenticated');
  if (!storageInfo.bucketExists) issues.push('Storage bucket missing');
  if (!storageInfo.canUpload) issues.push('Cannot upload to storage');
  if (!dbInfo.canSelectTemplates) issues.push('Cannot access templates');
  if (!dbInfo.canSelectThemes) issues.push('Cannot access themes');
  
  if (issues.length === 0) {
    console.log('✅ All checks passed!');
  } else {
    console.log('❌ Issues found:', issues);
  }
  
  return {
    auth: authInfo,
    storage: storageInfo,
    database: dbInfo,
    issues
  };
}

/**
 * Helper to format debug info for display
 */
export function formatDebugInfo(debugResult: any): string {
  return JSON.stringify(debugResult, null, 2);
}
