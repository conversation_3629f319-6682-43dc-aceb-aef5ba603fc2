"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  runFullDebug, 
  type AuthDebugInfo, 
  type StorageDebugInfo, 
  type DatabaseDebugInfo 
} from '@/utils/supabase-debug';
import { AlertCircle, CheckCircle, RefreshCw, User, Database, FolderOpen } from 'lucide-react';

interface DebugResult {
  auth: AuthDebugInfo;
  storage: StorageDebugInfo;
  database: DatabaseDebugInfo;
  issues: string[];
}

export function SupabaseDebug() {
  const [debugResult, setDebugResult] = useState<DebugResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  const runDebugCheck = async () => {
    setIsLoading(true);
    try {
      const result = await runFullDebug();
      setDebugResult(result);
      setLastChecked(new Date());
    } catch (error) {
      console.error('Debug check failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Run initial check
    runDebugCheck();
  }, []);

  const StatusBadge = ({ condition, label }: { condition: boolean; label: string }) => (
    <Badge variant={condition ? "default" : "destructive"} className="flex items-center gap-1">
      {condition ? <CheckCircle className="h-3 w-3" /> : <AlertCircle className="h-3 w-3" />}
      {label}
    </Badge>
  );

  if (!debugResult) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
            Supabase Debug Check
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">Running initial debug check...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <RefreshCw className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
              Supabase Debug Check
            </CardTitle>
            <Button onClick={runDebugCheck} disabled={isLoading} size="sm">
              {isLoading ? 'Checking...' : 'Refresh'}
            </Button>
          </div>
          {lastChecked && (
            <p className="text-sm text-muted-foreground">
              Last checked: {lastChecked.toLocaleTimeString()}
            </p>
          )}
        </CardHeader>
        <CardContent>
          {debugResult.issues.length === 0 ? (
            <div className="flex items-center gap-2 text-green-600">
              <CheckCircle className="h-5 w-5" />
              <span className="font-medium">All systems operational</span>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-red-600">
                <AlertCircle className="h-5 w-5" />
                <span className="font-medium">{debugResult.issues.length} issue(s) found</span>
              </div>
              <ul className="list-disc list-inside text-sm text-muted-foreground ml-7">
                {debugResult.issues.map((issue, index) => (
                  <li key={index}>{issue}</li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Authentication Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Authentication
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <StatusBadge condition={debugResult.auth.isAuthenticated} label="Authenticated" />
            <StatusBadge condition={debugResult.auth.sessionValid} label="Session Valid" />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">User ID:</span>
              <p className="text-muted-foreground font-mono">
                {debugResult.auth.userId || 'Not authenticated'}
              </p>
            </div>
            <div>
              <span className="font-medium">Email:</span>
              <p className="text-muted-foreground">
                {debugResult.auth.email || 'N/A'}
              </p>
            </div>
            <div>
              <span className="font-medium">Role:</span>
              <p className="text-muted-foreground">
                {debugResult.auth.role || 'N/A'}
              </p>
            </div>
          </div>

          {debugResult.auth.error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600">
                <strong>Error:</strong> {debugResult.auth.error}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Storage Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5" />
            Storage (profileimg bucket)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <StatusBadge condition={debugResult.storage.bucketExists} label="Bucket Exists" />
            <StatusBadge condition={debugResult.storage.bucketPublic} label="Public Access" />
            <StatusBadge condition={debugResult.storage.canUpload} label="Can Upload" />
            <StatusBadge condition={debugResult.storage.canRead} label="Can Read" />
          </div>

          {debugResult.storage.error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600">
                <strong>Error:</strong> {debugResult.storage.error}
              </p>
            </div>
          )}

          {!debugResult.storage.bucketExists && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-700">
                <strong>Action Required:</strong> Create the 'profileimg' bucket in Supabase Storage
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Database Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Database Access
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <StatusBadge condition={debugResult.database.canSelectTemplates} label="Templates" />
            <StatusBadge condition={debugResult.database.canSelectThemes} label="Themes" />
            <StatusBadge condition={debugResult.database.canSelectResumes} label="Resumes" />
            <StatusBadge condition={debugResult.database.canInsertResume} label="Create Resume" />
          </div>

          {debugResult.database.error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600">
                <strong>Error:</strong> {debugResult.database.error}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Fixes */}
      {debugResult.issues.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Quick Fixes</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {!debugResult.auth.isAuthenticated && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-sm text-blue-700">
                  <strong>Authentication Issue:</strong> Please log in to access all features.
                </p>
              </div>
            )}
            
            {!debugResult.storage.bucketExists && (
              <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <p className="text-sm text-orange-700">
                  <strong>Storage Issue:</strong> Run the SQL commands in 'supabase-rls-policies.sql' to create the storage bucket and policies.
                </p>
              </div>
            )}
            
            {!debugResult.database.canSelectTemplates && (
              <div className="p-3 bg-purple-50 border border-purple-200 rounded-lg">
                <p className="text-sm text-purple-700">
                  <strong>Database Issue:</strong> Apply RLS policies from 'supabase-rls-policies.sql' to fix table access.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
