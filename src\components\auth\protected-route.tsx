"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  useAuthStore,
  useIsAuthenticated,
  useAuthLoading,
  useAuthInitialized,
} from "@/stores/auth-store";
import { Spinner } from "../ui/kibo-ui/spinner";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
  loadingComponent?: React.ReactNode;
}

export function ProtectedRoute({
  children,
  requireAuth = true,
  redirectTo = "/login",
  loadingComponent,
}: ProtectedRouteProps) {
  const router = useRouter();
  const isAuthenticated = useIsAuthenticated();
  const isLoading = useAuthLoading();
  const isInitialized = useAuthInitialized();
  const checkAuth = useAuthStore((state) => state.checkAuth);

  useEffect(() => {
    if (!isInitialized) return;

    const handleAuth = async () => {
      if (requireAuth && !isAuthenticated) {
        // Double-check auth status before redirecting
        const user = await checkAuth();
        if (!user) {
          router.push(redirectTo);
        }
      }
    };

    handleAuth();
  }, [
    isAuthenticated,
    isInitialized,
    requireAuth,
    redirectTo,
    router,
    checkAuth,
  ]);

  // Show loading while initializing or checking auth
  if (!isInitialized || isLoading) {
    return (
      loadingComponent || (
        <div className="min-h-screen bg-gradient-to-br from-background via-primary/5 to-secondary/10 flex items-center justify-center">
          <div className="flex flex-col items-center space-y-8">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/30 to-secondary/30 rounded-full blur-xl animate-pulse"></div>
              <div className="relative flex items-center justify-center">
                <Spinner />
              </div>
            </div>
            <div className="text-center space-y-2">
              <h3 className="text-lg font-semibold text-foreground">
                Loading Verfolia
              </h3>
              <p className="text-muted-foreground text-sm">
                Please wait while we set things up
              </p>
            </div>
          </div>
        </div>
      )
    );
  }

  // If auth is required but user is not authenticated, don't render children
  if (requireAuth && !isAuthenticated) {
    return null;
  }

  return <>{children}</>;
}
