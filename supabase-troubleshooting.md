# Supabase RLS Policy Troubleshooting Guide

## Issue Summary
You're experiencing Row-Level Security (RLS) policy violations in Supabase, specifically:
- `Failed to upload image: new row violates row-level security policy`
- Database access issues with folders/tables
- Anonymous access being blocked

## Quick Fix Steps

### 1. Apply <PERSON><PERSON> Policies
Run the SQL commands in `supabase-rls-policies.sql` in your Supabase SQL Editor:
1. Go to your Supabase Dashboard
2. Navigate to SQL Editor
3. Copy and paste the contents of `supabase-rls-policies.sql`
4. Execute the queries

### 2. Verify Storage Bucket Configuration
In Supabase Dashboard → Storage:
1. Check if `profileimg` bucket exists
2. Ensure it's set to **Public**
3. Verify file size limit is 5MB
4. Check allowed MIME types include: `image/jpeg`, `image/jpg`, `image/png`, `image/webp`

### 3. Check Authentication Status
Ensure users are properly authenticated before uploading images:

```typescript
// In your component, verify user is authenticated
const { user } = useAuth();
if (!user) {
  // Redirect to login or show auth required message
  return;
}
```

## Common Issues & Solutions

### Issue 1: "new row violates row-level security policy"
**Cause**: RLS policies are blocking the operation
**Solution**: Apply the RLS policies from the SQL file

### Issue 2: Anonymous access blocked
**Cause**: User not authenticated or session expired
**Solutions**:
- Check if user is logged in
- Refresh authentication session
- Verify JWT token is valid

### Issue 3: Storage upload fails
**Cause**: Missing storage policies or bucket misconfiguration
**Solutions**:
- Ensure bucket is public
- Apply storage policies from SQL file
- Check file size and type restrictions

## Environment Variables Check
Ensure these are set in your `.env.local`:
```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
```

## Testing Authentication
Add this debug component to test auth status:

```typescript
// components/AuthDebug.tsx
import { useAuth } from '@/hooks/use-auth';

export function AuthDebug() {
  const { user, isLoading } = useAuth();
  
  if (isLoading) return <div>Loading auth...</div>;
  
  return (
    <div className="p-4 bg-gray-100 rounded">
      <h3>Auth Status</h3>
      <p>User ID: {user?.id || 'Not authenticated'}</p>
      <p>Email: {user?.email || 'N/A'}</p>
      <p>Role: {user?.role || 'N/A'}</p>
    </div>
  );
}
```

## Manual Policy Creation (Alternative)
If the SQL file doesn't work, create policies manually in Supabase Dashboard:

### Storage Policies (Storage → Policies):
1. **Insert Policy**: Allow authenticated users to upload to profileimg
2. **Select Policy**: Allow public read access to profileimg
3. **Update Policy**: Allow users to update their own files
4. **Delete Policy**: Allow users to delete their own files

### Table Policies (Database → Tables → [table] → Policies):
1. **Resumes Table**: 
   - Insert: Users can insert own resumes
   - Select: Users can view own + public resumes
   - Update: Users can update own resumes
   - Delete: Users can delete own resumes

## Verification Queries
Run these in SQL Editor to verify setup:

```sql
-- Check if policies exist
SELECT * FROM pg_policies WHERE schemaname = 'public';

-- Check storage policies
SELECT * FROM storage.policies;

-- Check bucket config
SELECT * FROM storage.buckets WHERE id = 'profileimg';

-- Test current user
SELECT auth.uid(), auth.role();
```

## Additional Debugging
Enable detailed error logging in your image upload component:

```typescript
try {
  const result = await imageUploadService.uploadImage(base64Data, user.id);
  console.log('Upload success:', result);
} catch (error) {
  console.error('Upload error details:', {
    error,
    message: error.message,
    user: user?.id,
    bucket: 'profileimg'
  });
}
```

## Contact Support
If issues persist:
1. Check Supabase Dashboard → Logs for detailed error messages
2. Verify your project's RLS settings
3. Consider temporarily disabling RLS for testing (NOT recommended for production)

## Security Notes
- Never disable RLS in production
- Always validate user permissions
- Use service role key only for server-side operations
- Keep anon key public-safe (no sensitive operations)
