"use client";

import { SupabaseDebug } from '@/components/debug/SupabaseDebug';

export default function DebugPage() {
  return (
    <div className="min-h-screen bg-background py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold tracking-tight">Supabase Debug Dashboard</h1>
          <p className="text-muted-foreground mt-2">
            Diagnose and fix RLS policy and authentication issues
          </p>
        </div>
        
        <SupabaseDebug />
        
        <div className="mt-8 text-center text-sm text-muted-foreground">
          <p>
            This debug page helps identify and resolve Supabase configuration issues.
            <br />
            Remove this page in production.
          </p>
        </div>
      </div>
    </div>
  );
}
