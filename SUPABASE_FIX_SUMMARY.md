# Supabase RLS Policy Fix - Complete Solution

## 🚨 Problem Summary
You're experiencing Row-Level Security (RLS) policy violations in Supabase:
- `Failed to upload image: new row violates row-level security policy`
- Database access issues with authenticated/anonymous users
- Storage bucket permission problems

## 🔧 Solution Steps

### Step 1: Apply RLS Policies (CRITICAL)
1. Open your **Supabase Dashboard**
2. Go to **SQL Editor**
3. Copy and paste the entire contents of `supabase-rls-policies.sql`
4. Click **Run** to execute all policies

### Step 2: Verify Storage Configuration
1. Go to **Storage** in Supabase Dashboard
2. Check if `profileimg` bucket exists
3. If not, it will be created by the SQL script
4. Ensure bucket is marked as **Public**

### Step 3: Test the Fix
1. Visit `/debug` page in your app (http://localhost:3000/debug)
2. Run the debug check to verify all systems
3. Look for any remaining issues

### Step 4: Test Image Upload
1. Go to create resume page
2. Try uploading a profile image
3. Should now work without RLS errors

## 📁 Files Created/Modified

### New Files:
- `supabase-rls-policies.sql` - Complete RLS policy setup
- `supabase-troubleshooting.md` - Detailed troubleshooting guide
- `src/utils/supabase-debug.ts` - Debug utilities
- `src/components/debug/SupabaseDebug.tsx` - Debug dashboard
- `src/app/debug/page.tsx` - Debug page

### Modified Files:
- `src/components/ui/image-upload.tsx` - Better error handling

## 🔍 Debug Dashboard
Access the debug dashboard at: **http://localhost:3000/debug**

This will show you:
- ✅ Authentication status
- ✅ Storage bucket permissions
- ✅ Database table access
- ✅ Specific issues and fixes

## 🛠️ What the SQL Script Does

### Storage Policies:
- Creates `profileimg` bucket if missing
- Allows authenticated users to upload images
- Allows public read access for resume viewing
- Allows users to manage their own images

### Database Policies:
- **Resumes**: Users can CRUD their own resumes, public can view public resumes
- **Templates**: Public read access
- **Themes**: Public read access
- **Analytics**: Public insert, users view own analytics

### Functions:
- `generate_unique_slug()` - Creates unique resume slugs

## 🚨 Common Issues & Quick Fixes

### Issue: "Authentication required"
**Fix**: User needs to log in before uploading images

### Issue: "Storage service unavailable"
**Fix**: Run the SQL script to create storage policies

### Issue: "Bucket not found"
**Fix**: SQL script creates the bucket automatically

### Issue: Database access denied
**Fix**: SQL script applies all necessary RLS policies

## 🔐 Security Notes
- RLS policies ensure users can only access their own data
- Public access is only for viewing public resumes and templates
- Storage is secured but allows public read for resume display
- All policies follow security best practices

## 🧪 Testing Checklist
After applying the fix, test these features:

- [ ] User can log in/out
- [ ] User can upload profile image
- [ ] User can create resume
- [ ] User can view their own resumes
- [ ] Public can view public resumes
- [ ] Templates and themes load properly
- [ ] Analytics tracking works

## 🚀 Production Deployment
Before deploying to production:

1. **Remove debug page**: Delete `src/app/debug/` folder
2. **Remove debug utilities**: Optional, but recommended for security
3. **Test thoroughly**: Ensure all features work in production environment
4. **Monitor logs**: Check Supabase logs for any remaining issues

## 📞 Support
If issues persist after applying these fixes:

1. Check the debug dashboard for specific error details
2. Review Supabase Dashboard → Logs for detailed error messages
3. Verify environment variables are correctly set
4. Ensure you're using the correct Supabase project

## 🔄 Rollback Plan
If something goes wrong:

1. You can disable RLS temporarily (NOT recommended for production):
   ```sql
   ALTER TABLE resumes DISABLE ROW LEVEL SECURITY;
   ```

2. Or remove specific policies:
   ```sql
   DROP POLICY "policy_name" ON table_name;
   ```

## ✅ Success Indicators
You'll know the fix worked when:
- Image uploads complete without errors
- Debug dashboard shows all green checkmarks
- Users can create and view resumes normally
- No RLS policy violation errors in console

---

**Next Steps**: Run the SQL script, test with the debug dashboard, and verify image uploads work correctly.
